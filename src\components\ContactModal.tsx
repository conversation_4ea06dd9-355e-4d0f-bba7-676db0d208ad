"use client"

import { useState } from 'react'
import { useTranslations } from 'next-intl'
import { Button } from '@/components/ui/button'
import { Mail } from 'lucide-react'

export default function ContactModal() {
  const [contactModalOpen, setContactModalOpen] = useState(false)
  const t = useTranslations()

  return (
    <>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setContactModalOpen(true)}
        className="text-slate-300 hover:text-orange-400"
      >
        <Mail className="w-4 h-4 mr-2" />
        {t('footer.contact')}
      </Button>

      {/* Contact Modal */}
      {contactModalOpen && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-slate-800 rounded-lg p-6 max-w-md w-full">
            <h3 className="text-white text-lg font-semibold mb-4">{t('modal.contactTitle')}</h3>
            <p className="text-slate-300 mb-6">{t('modal.contactEmail')}</p>
            <Button
              onClick={() => setContactModalOpen(false)}
              className="w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600"
            >
              {t('buttons.close')}
            </Button>
          </div>
        </div>
      )}
    </>
  )
}
