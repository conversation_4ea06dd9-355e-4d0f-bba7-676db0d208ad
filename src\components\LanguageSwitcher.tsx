"use client";

import { useLocale } from 'next-intl';
import { useRouter, usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Globe } from 'lucide-react';
import { useState, useTransition } from 'react';

const languages = [
  { code: 'zh', name: '中文', flag: '🇨🇳' },
  { code: 'en', name: 'English', flag: '🇺🇸' }
];

export default function LanguageSwitcher() {
  const router = useRouter();
  const pathname = usePathname();
  const [isPending, startTransition] = useTransition();
  const [isOpen, setIsOpen] = useState(false);

  // Extract locale from pathname more reliably
  const getCurrentLocale = () => {
    const segments = pathname.split('/');
    const localeFromPath = segments[1];
    return languages.find(lang => lang.code === localeFromPath)?.code || 'zh';
  };

  const currentLocale = getCurrentLocale();
  const currentLanguage = languages.find(lang => lang.code === currentLocale) || languages[0];

  const switchLanguage = (newLocale: string) => {
    if (newLocale === currentLocale) return;

    startTransition(() => {
      // Build new pathname by replacing the locale segment
      const segments = pathname.split('/');
      segments[1] = newLocale; // Replace the locale segment
      const newPathname = segments.join('/');

      router.push(newPathname);

      // Store language preference in both localStorage and cookies
      localStorage.setItem('preferred-language', newLocale);
      document.cookie = `preferred-language=${newLocale}; path=/; max-age=${60 * 60 * 24 * 365}`; // 1 year
    });

    setIsOpen(false);
  };

  return (
    <div className="relative">
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 text-slate-300 hover:text-orange-400 hover:bg-slate-800/50"
        disabled={isPending}
      >
        <Globe className="w-4 h-4" />
        <span className="hidden sm:inline">{currentLanguage.name}</span>
        <span className="sm:hidden">{currentLanguage.flag}</span>
      </Button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-40" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Dropdown */}
          <div className="absolute right-0 mt-2 w-48 bg-slate-800 border border-slate-700 rounded-lg shadow-lg z-50">
            <div className="py-2">
              {languages.map((language) => (
                <button
                  key={language.code}
                  onClick={() => switchLanguage(language.code)}
                  className={`w-full px-4 py-2 text-left flex items-center space-x-3 hover:bg-slate-700 transition-colors ${
                    language.code === currentLocale
                      ? 'text-orange-400 bg-slate-700/50'
                      : 'text-slate-300'
                  }`}
                  disabled={isPending}
                >
                  <span className="text-lg">{language.flag}</span>
                  <span>{language.name}</span>
                  {language.code === currentLocale && (
                    <span className="ml-auto text-orange-400">✓</span>
                  )}
                </button>
              ))}
            </div>
          </div>
        </>
      )}
    </div>
  );
}
