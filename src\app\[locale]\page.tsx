import { locales } from '@/i18n'

export async function generateStaticParams() {
  return locales.map((locale) => ({ locale }));
}

export default async function Home({
  params
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;

  return (
    <div style={{ padding: '20px', backgroundColor: '#1a1a1a', color: 'white', minHeight: '100vh' }}>
      <h1>🎆 全球烟花秀日历</h1>
      <p>Current locale: {locale}</p>
      <p>Page is working! 页面正常工作！</p>
      <p>This is a test page to verify routing works.</p>
    </div>
  );
}
