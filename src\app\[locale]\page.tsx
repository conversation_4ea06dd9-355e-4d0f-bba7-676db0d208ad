import { getTranslations } from 'next-intl/server'
import { locales } from '@/i18n'
import Image from 'next/image'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { Calendar, MapPin, Clock, Star, Globe, Mail, MessageCircle } from 'lucide-react'
import DatePickerTimeline from '@/components/date-picker-timeline'
import LanguageSwitcher from '@/components/LanguageSwitcher'
import ContactModal from '@/components/ContactModal'

export async function generateStaticParams() {
  return locales.map((locale) => ({ locale }));
}

export default async function Home({
  params
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const t = await getTranslations({ locale });

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* Navigation */}
      <nav className="fixed top-0 left-0 right-0 z-50 bg-slate-900/80 backdrop-blur-md border-b border-slate-700/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-2">
              <span className="text-2xl">🎆</span>
              <span className="text-xl font-bold text-gradient-goldorange">{t('navigation.title')}</span>
            </div>
            <div className="hidden md:flex items-center space-x-8">
              <a href="#calendar" className="text-slate-300 hover:text-orange-400 transition">{t('navigation.calendar')}</a>
              <a href="#events" className="text-slate-300 hover:text-orange-400 transition">{t('navigation.events')}</a>
              <a href="#guides" className="text-slate-300 hover:text-orange-400 transition">{t('navigation.guides')}</a>
              <a href="#archives" className="text-slate-300 hover:text-orange-400 transition">{t('navigation.archives')}</a>
              <a href="#submit" className="text-slate-300 hover:text-orange-400 transition">{t('navigation.submit')}</a>
            </div>
            <LanguageSwitcher />
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="pt-24 pb-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            <span className="text-white">{t('hero.title')}</span>
            <br />
            <span className="text-gradient-goldorange">{t('hero.titleHighlight')}</span>
          </h1>
          <p className="text-xl text-slate-300 mb-8 max-w-3xl mx-auto">
            {t('hero.description')}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600">
              <Calendar className="w-5 h-5 mr-2" />
              {t('navigation.calendar')}
            </Button>
            <Button size="lg" variant="outline" className="border-orange-400 text-orange-400 hover:bg-orange-400 hover:text-white">
              <Globe className="w-5 h-5 mr-2" />
              {t('navigation.events')}
            </Button>
          </div>
        </div>
      </section>

      {/* Calendar Section */}
      <section id="calendar" className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">{t('sections.calendar.title')}</h2>
            <p className="text-xl text-slate-300">{t('sections.calendar.description')}</p>
          </div>
          <div className="bg-slate-800/50 rounded-2xl p-6 backdrop-blur-sm border border-slate-700/50">
            <DatePickerTimeline />
          </div>
        </div>
      </section>

      {/* Events Section */}
      <section id="events" className="py-16 px-4 sm:px-6 lg:px-8 bg-slate-800/30">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">{t('sections.events.title')}</h2>
            <p className="text-xl text-slate-300">{t('sections.events.description')}</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Sydney Event */}
            <Card className="bg-slate-800/50 border-slate-700/50 hover:bg-slate-700/50 transition-all duration-300 group">
              <div className="relative h-48 overflow-hidden rounded-t-lg">
                <Image
                  src="https://images.unsplash.com/photo-1514525253161-7a46d19cd819?w=600&h=400&fit=crop"
                  alt={t('events.sydney.title')}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute top-4 left-4">
                  <span className="bg-orange-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                    {t('events.sydney.theme')}
                  </span>
                </div>
              </div>
              <CardHeader>
                <CardTitle className="text-white">{t('events.sydney.title')}</CardTitle>
                <div className="flex items-center text-slate-400 text-sm">
                  <MapPin className="w-4 h-4 mr-1" />
                  {t('events.sydney.location')}
                </div>
              </CardHeader>
              <CardContent>
                <Button className="w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600">
                  {t('buttons.viewDetails')}
                </Button>
              </CardContent>
            </Card>

            {/* Dubai Event */}
            <Card className="bg-slate-800/50 border-slate-700/50 hover:bg-slate-700/50 transition-all duration-300 group">
              <div className="relative h-48 overflow-hidden rounded-t-lg">
                <Image
                  src="https://images.unsplash.com/photo-1512453979798-5ea266f8880c?w=600&h=400&fit=crop"
                  alt={t('events.dubai.title')}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute top-4 left-4">
                  <span className="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                    {t('events.dubai.theme')}
                  </span>
                </div>
              </div>
              <CardHeader>
                <CardTitle className="text-white">{t('events.dubai.title')}</CardTitle>
                <div className="flex items-center text-slate-400 text-sm">
                  <MapPin className="w-4 h-4 mr-1" />
                  {t('events.dubai.location')}
                </div>
              </CardHeader>
              <CardContent>
                <Button className="w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600">
                  {t('buttons.viewDetails')}
                </Button>
              </CardContent>
            </Card>

            {/* London Event */}
            <Card className="bg-slate-800/50 border-slate-700/50 hover:bg-slate-700/50 transition-all duration-300 group">
              <div className="relative h-48 overflow-hidden rounded-t-lg">
                <Image
                  src="https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=600&h=400&fit=crop"
                  alt={t('events.london.title')}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute top-4 left-4">
                  <span className="bg-purple-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                    {t('events.london.theme')}
                  </span>
                </div>
              </div>
              <CardHeader>
                <CardTitle className="text-white">{t('events.london.title')}</CardTitle>
                <div className="flex items-center text-slate-400 text-sm">
                  <MapPin className="w-4 h-4 mr-1" />
                  {t('events.london.location')}
                </div>
              </CardHeader>
              <CardContent>
                <Button className="w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600">
                  {t('buttons.viewDetails')}
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Travel Guides Section */}
      <section id="guides" className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">{t('sections.guides.title')}</h2>
            <p className="text-xl text-slate-300">{t('sections.guides.description')}</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Sydney Guide */}
            <Card className="bg-slate-800/50 border-slate-700/50 hover:bg-slate-700/50 transition-all duration-300 group">
              <div className="relative h-48 overflow-hidden rounded-t-lg">
                <Image
                  src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=600&h=400&fit=crop"
                  alt={t('guides.sydney.title')}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute top-4 right-4">
                  <span className="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                    {t('guides.sydney.readTime')}
                  </span>
                </div>
              </div>
              <CardHeader>
                <CardTitle className="text-white">{t('guides.sydney.title')}</CardTitle>
                <p className="text-slate-400 text-sm">{t('guides.sydney.description')}</p>
              </CardHeader>
              <CardContent>
                <Link href={`/${locale}/travel-guide-1`}>
                  <Button className="w-full bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600">
                    {t('buttons.readGuide')}
                  </Button>
                </Link>
              </CardContent>
            </Card>

            {/* Dubai Guide */}
            <Card className="bg-slate-800/50 border-slate-700/50 hover:bg-slate-700/50 transition-all duration-300 group">
              <div className="relative h-48 overflow-hidden rounded-t-lg">
                <Image
                  src="https://images.unsplash.com/photo-1512453979798-5ea266f8880c?w=600&h=400&fit=crop"
                  alt={t('guides.dubai.title')}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute top-4 right-4">
                  <span className="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                    {t('guides.dubai.readTime')}
                  </span>
                </div>
              </div>
              <CardHeader>
                <CardTitle className="text-white">{t('guides.dubai.title')}</CardTitle>
                <p className="text-slate-400 text-sm">{t('guides.dubai.description')}</p>
              </CardHeader>
              <CardContent>
                <Link href={`/${locale}/travel-guide-2`}>
                  <Button className="w-full bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600">
                    {t('buttons.readGuide')}
                  </Button>
                </Link>
              </CardContent>
            </Card>

            {/* Europe Guide */}
            <Card className="bg-slate-800/50 border-slate-700/50 hover:bg-slate-700/50 transition-all duration-300 group">
              <div className="relative h-48 overflow-hidden rounded-t-lg">
                <Image
                  src="https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=600&h=400&fit=crop"
                  alt={t('guides.europe.title')}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute top-4 right-4">
                  <span className="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                    {t('guides.europe.readTime')}
                  </span>
                </div>
              </div>
              <CardHeader>
                <CardTitle className="text-white">{t('guides.europe.title')}</CardTitle>
                <p className="text-slate-400 text-sm">{t('guides.europe.description')}</p>
              </CardHeader>
              <CardContent>
                <Link href={`/${locale}/travel-guide-3`}>
                  <Button className="w-full bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600">
                    {t('buttons.readGuide')}
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Archives Section */}
      <section id="archives" className="py-16 px-4 sm:px-6 lg:px-8 bg-slate-800/30">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">{t('sections.archives.title')}</h2>
            <p className="text-xl text-slate-300">{t('sections.archives.description')}</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Beijing Olympics */}
            <Card className="bg-slate-800/50 border-slate-700/50 hover:bg-slate-700/50 transition-all duration-300 group">
              <div className="flex flex-col md:flex-row">
                <div className="relative h-48 md:h-auto md:w-1/2 overflow-hidden">
                  <Image
                    src="https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=600&h=400&fit=crop"
                    alt={t('archives.beijing.title')}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute top-4 left-4">
                    <span className="bg-yellow-500 text-black px-3 py-1 rounded-full text-sm font-medium">
                      {t('archives.beijing.year')}
                    </span>
                  </div>
                </div>
                <div className="md:w-1/2 p-6">
                  <CardHeader className="p-0 mb-4">
                    <CardTitle className="text-white">{t('archives.beijing.title')}</CardTitle>
                    <div className="flex items-center text-orange-400 text-sm">
                      <Star className="w-4 h-4 mr-1" />
                      {t('archives.beijing.significance')}
                    </div>
                  </CardHeader>
                  <CardContent className="p-0">
                    <p className="text-slate-300 mb-4">{t('archives.beijing.description')}</p>
                    <Button className="bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-black">
                      {t('buttons.learnMore')}
                    </Button>
                  </CardContent>
                </div>
              </div>
            </Card>

            {/* London 2000 */}
            <Card className="bg-slate-800/50 border-slate-700/50 hover:bg-slate-700/50 transition-all duration-300 group">
              <div className="flex flex-col md:flex-row">
                <div className="relative h-48 md:h-auto md:w-1/2 overflow-hidden">
                  <Image
                    src="https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=600&h=400&fit=crop"
                    alt={t('archives.london2000.title')}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute top-4 left-4">
                    <span className="bg-purple-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                      {t('archives.london2000.year')}
                    </span>
                  </div>
                </div>
                <div className="md:w-1/2 p-6">
                  <CardHeader className="p-0 mb-4">
                    <CardTitle className="text-white">{t('archives.london2000.title')}</CardTitle>
                    <div className="flex items-center text-orange-400 text-sm">
                      <Star className="w-4 h-4 mr-1" />
                      {t('archives.london2000.significance')}
                    </div>
                  </CardHeader>
                  <CardContent className="p-0">
                    <p className="text-slate-300 mb-4">{t('archives.london2000.description')}</p>
                    <Button className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600">
                      {t('buttons.learnMore')}
                    </Button>
                  </CardContent>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </section>

      {/* Submit Section */}
      <section id="submit" className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">{t('sections.submit.title')}</h2>
            <p className="text-xl text-slate-300">{t('sections.submit.description')}</p>
          </div>
          <Card className="bg-slate-800/50 border-slate-700/50">
            <CardHeader>
              <CardTitle className="text-white text-center">{t('form.title')}</CardTitle>
              <p className="text-slate-300 text-center">{t('form.description')}</p>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">
                    {t('form.eventName')}
                  </label>
                  <input
                    type="text"
                    placeholder={t('form.eventNamePlaceholder')}
                    className="w-full px-4 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-orange-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">
                    {t('form.location')}
                  </label>
                  <input
                    type="text"
                    placeholder={t('form.locationPlaceholder')}
                    className="w-full px-4 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-orange-500"
                  />
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  {t('form.experience')}
                </label>
                <textarea
                  rows={4}
                  placeholder={t('form.experiencePlaceholder')}
                  className="w-full px-4 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-orange-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  {t('form.uploadPhotos')}
                </label>
                <div className="border-2 border-dashed border-slate-600 rounded-lg p-8 text-center hover:border-orange-400 transition-colors">
                  <div className="text-slate-400">
                    <svg className="mx-auto h-12 w-12 mb-4" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                      <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                    <p className="text-lg">{t('form.uploadText')}</p>
                  </div>
                </div>
              </div>
              <Button className="w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-lg py-3">
                {t('form.submit')}
              </Button>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-slate-900 border-t border-slate-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="md:col-span-2">
              <div className="flex items-center space-x-2 mb-4">
                <span className="text-2xl">🎆</span>
                <span className="text-xl font-bold text-gradient-goldorange">{t('navigation.title')}</span>
              </div>
              <p className="text-slate-300 mb-6 max-w-md">
                {t('footer.description')}
              </p>
              <div className="flex space-x-4">
                <ContactModal />
              </div>
            </div>
            <div>
              <h3 className="text-white font-semibold mb-4">{t('footer.quickLinks')}</h3>
              <ul className="space-y-2">
                <li><a href="#calendar" className="text-slate-300 hover:text-orange-400 transition">{t('navigation.calendar')}</a></li>
                <li><a href="#events" className="text-slate-300 hover:text-orange-400 transition">{t('navigation.events')}</a></li>
                <li><a href="#guides" className="text-slate-300 hover:text-orange-400 transition">{t('navigation.guides')}</a></li>
                <li><a href="#archives" className="text-slate-300 hover:text-orange-400 transition">{t('navigation.archives')}</a></li>
              </ul>
            </div>
            <div>
              <h3 className="text-white font-semibold mb-4">{t('footer.contactInfo')}</h3>
              <ul className="space-y-2">
                <li className="text-slate-300">{t('footer.email')}</li>
                <li className="text-slate-300">{t('footer.service')}</li>
              </ul>
            </div>
          </div>
          <Separator className="my-8 bg-slate-700" />
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-slate-400 text-sm">{t('footer.copyright')}</p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <a href="#" className="text-slate-400 hover:text-orange-400 text-sm transition">{t('footer.privacy')}</a>
              <a href="#" className="text-slate-400 hover:text-orange-400 text-sm transition">{t('footer.terms')}</a>
              <a href="#" className="text-slate-400 hover:text-orange-400 text-sm transition">{t('footer.sitemap')}</a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
